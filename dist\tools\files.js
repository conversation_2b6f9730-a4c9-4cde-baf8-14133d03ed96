"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileTool = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const glob_1 = require("glob");
const nanoid_1 = require("nanoid");
const logger_1 = require("../utils/logger");
const chokidar_1 = __importDefault(require("chokidar"));
const events_1 = require("events");
class FileTool extends events_1.EventEmitter {
    maxFileSize = 50 * 1024 * 1024; // 50MB default
    backupDir = '.kritrima-backups';
    watchers = new Map();
    constructor() {
        super();
    }
    async read(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const encoding = args.encoding || 'utf-8';
            const lines = args.lines;
            const offset = args.offset;
            if (!filePath) {
                throw new Error('File path is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Security check - prevent reading outside working directory
            if (!this.isPathSafe(resolvedPath)) {
                throw new Error('Access denied: Path is outside allowed directory');
            }
            // Check if file exists
            await promises_1.default.access(resolvedPath);
            // Get file stats
            const stats = await promises_1.default.stat(resolvedPath);
            if (stats.isDirectory()) {
                throw new Error('Path is a directory, not a file');
            }
            // Check file size
            if (stats.size > this.maxFileSize) {
                throw new Error(`File too large (${this.formatBytes(stats.size)}). Maximum allowed: ${this.formatBytes(this.maxFileSize)}`);
            }
            let content;
            // Handle partial reading if lines or offset specified
            if (lines !== undefined || offset !== undefined) {
                content = await this.readFilePartial(resolvedPath, encoding, lines, offset);
            }
            else {
                content = await promises_1.default.readFile(resolvedPath, { encoding: encoding });
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: content,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    size: stats.size,
                    encoding,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async write(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const content = args.content;
            const encoding = args.encoding || 'utf-8';
            const backup = args.backup;
            if (!filePath) {
                throw new Error('File path is required');
            }
            if (content === undefined) {
                throw new Error('Content is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Security check
            if (!this.isPathSafe(resolvedPath)) {
                throw new Error('Access denied: Path is outside allowed directory');
            }
            // Create backup if requested and file exists
            if (backup) {
                try {
                    await promises_1.default.access(resolvedPath);
                    await this.createBackup(resolvedPath);
                }
                catch (error) {
                    // File doesn't exist, no backup needed
                }
            }
            // Ensure directory exists
            const dir = path_1.default.dirname(resolvedPath);
            await promises_1.default.mkdir(dir, { recursive: true });
            // Write file
            await promises_1.default.writeFile(resolvedPath, content, { encoding: encoding });
            const stats = await promises_1.default.stat(resolvedPath);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `File written successfully: ${resolvedPath} (${stats.size} bytes)`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    size: stats.size,
                    encoding,
                    backup,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async create(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const content = args.content || '';
            const encoding = args.encoding || 'utf-8';
            if (!filePath) {
                throw new Error('File path is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Check if file already exists
            try {
                await promises_1.default.access(resolvedPath);
                throw new Error('File already exists');
            }
            catch (error) {
                // File doesn't exist, which is what we want
                if (error.code !== 'ENOENT') {
                    throw error;
                }
            }
            // Ensure directory exists
            const dir = path_1.default.dirname(resolvedPath);
            await promises_1.default.mkdir(dir, { recursive: true });
            // Create file
            await promises_1.default.writeFile(resolvedPath, content, { encoding: encoding });
            const stats = await promises_1.default.stat(resolvedPath);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `File created successfully: ${resolvedPath} (${stats.size} bytes)`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    size: stats.size,
                    encoding,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    // Additional methods will be added in the next chunk
    async readFilePartial(filePath, encoding, lines, offset) {
        const content = await promises_1.default.readFile(filePath, { encoding });
        const allLines = content.split('\n');
        const startLine = offset || 0;
        const endLine = lines ? startLine + lines : allLines.length;
        return allLines.slice(startLine, endLine).join('\n');
    }
    isPathSafe(filePath) {
        const cwd = process.cwd();
        const resolved = path_1.default.resolve(filePath);
        return resolved.startsWith(cwd);
    }
    formatBytes(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0)
            return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    async createBackup(filePath) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path_1.default.join(this.backupDir, `${path_1.default.basename(filePath)}.${timestamp}.backup`);
        await promises_1.default.mkdir(this.backupDir, { recursive: true });
        await promises_1.default.copyFile(filePath, backupPath);
        return backupPath;
    }
    async delete(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const force = args.force;
            const backup = args.backup;
            if (!filePath) {
                throw new Error('File path is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Security check
            if (!this.isPathSafe(resolvedPath)) {
                throw new Error('Access denied: Path is outside allowed directory');
            }
            // Check if file exists
            await promises_1.default.access(resolvedPath);
            const stats = await promises_1.default.stat(resolvedPath);
            // Create backup if requested
            if (backup && stats.isFile()) {
                await this.createBackup(resolvedPath);
            }
            // Delete file or directory
            if (stats.isDirectory()) {
                if (force) {
                    await promises_1.default.rm(resolvedPath, { recursive: true, force: true });
                }
                else {
                    await promises_1.default.rmdir(resolvedPath);
                }
            }
            else {
                await promises_1.default.unlink(resolvedPath);
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `${stats.isDirectory() ? 'Directory' : 'File'} deleted successfully: ${resolvedPath}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    type: stats.isDirectory() ? 'directory' : 'file',
                    backup,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async move(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const sourcePath = args.source;
            const destPath = args.destination;
            const backup = args.backup;
            if (!sourcePath || !destPath) {
                throw new Error('Source and destination paths are required');
            }
            const resolvedSource = path_1.default.resolve(sourcePath);
            const resolvedDest = path_1.default.resolve(destPath);
            // Security checks
            if (!this.isPathSafe(resolvedSource) || !this.isPathSafe(resolvedDest)) {
                throw new Error('Access denied: Path is outside allowed directory');
            }
            // Check if source exists
            await promises_1.default.access(resolvedSource);
            // Create backup of destination if it exists and backup is requested
            if (backup) {
                try {
                    await promises_1.default.access(resolvedDest);
                    await this.createBackup(resolvedDest);
                }
                catch (error) {
                    // Destination doesn't exist, no backup needed
                }
            }
            // Ensure destination directory exists
            const destDir = path_1.default.dirname(resolvedDest);
            await promises_1.default.mkdir(destDir, { recursive: true });
            // Move file/directory
            await promises_1.default.rename(resolvedSource, resolvedDest);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `Moved successfully: ${resolvedSource} → ${resolvedDest}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    source: resolvedSource,
                    destination: resolvedDest,
                    backup,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async copy(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const sourcePath = args.source;
            const destPath = args.destination;
            const recursive = args.recursive;
            const backup = args.backup;
            if (!sourcePath || !destPath) {
                throw new Error('Source and destination paths are required');
            }
            const resolvedSource = path_1.default.resolve(sourcePath);
            const resolvedDest = path_1.default.resolve(destPath);
            // Security checks
            if (!this.isPathSafe(resolvedSource) || !this.isPathSafe(resolvedDest)) {
                throw new Error('Access denied: Path is outside allowed directory');
            }
            // Check if source exists
            await promises_1.default.access(resolvedSource);
            const sourceStats = await promises_1.default.stat(resolvedSource);
            // Create backup of destination if it exists and backup is requested
            if (backup) {
                try {
                    await promises_1.default.access(resolvedDest);
                    await this.createBackup(resolvedDest);
                }
                catch (error) {
                    // Destination doesn't exist, no backup needed
                }
            }
            // Ensure destination directory exists
            const destDir = path_1.default.dirname(resolvedDest);
            await promises_1.default.mkdir(destDir, { recursive: true });
            // Copy file or directory
            if (sourceStats.isDirectory()) {
                if (!recursive) {
                    throw new Error('Cannot copy directory without recursive flag');
                }
                await this.copyDirectory(resolvedSource, resolvedDest);
            }
            else {
                await promises_1.default.copyFile(resolvedSource, resolvedDest);
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `Copied successfully: ${resolvedSource} → ${resolvedDest}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    source: resolvedSource,
                    destination: resolvedDest,
                    type: sourceStats.isDirectory() ? 'directory' : 'file',
                    recursive,
                    backup,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async copyDirectory(source, dest) {
        await promises_1.default.mkdir(dest, { recursive: true });
        const entries = await promises_1.default.readdir(source, { withFileTypes: true });
        for (const entry of entries) {
            const sourcePath = path_1.default.join(source, entry.name);
            const destPath = path_1.default.join(dest, entry.name);
            if (entry.isDirectory()) {
                await this.copyDirectory(sourcePath, destPath);
            }
            else {
                await promises_1.default.copyFile(sourcePath, destPath);
            }
        }
    }
    async search(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const pattern = args.pattern;
            const cwd = args.cwd || process.cwd();
            const maxDepth = args.maxDepth || 10;
            if (!pattern) {
                throw new Error('Search pattern is required');
            }
            const files = await (0, glob_1.glob)(pattern, {
                cwd: path_1.default.resolve(cwd),
                maxDepth,
                dot: false,
            });
            const results = files.slice(0, 100); // Limit results for performance
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: results.length > 0
                    ? `Found ${results.length} files:\n${results.join('\n')}`
                    : 'No files found matching the pattern',
                executionTime: Date.now() - startTime,
                metadata: {
                    pattern,
                    cwd,
                    maxDepth,
                    resultCount: results.length,
                    results,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async grep(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const pattern = args.pattern;
            const filePath = args.path;
            const recursive = args.recursive;
            const ignoreCase = args.ignoreCase;
            const lineNumbers = args.lineNumbers;
            const maxResults = args.maxResults || 100;
            if (!pattern) {
                throw new Error('Search pattern is required');
            }
            if (!filePath) {
                throw new Error('File path is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Security check
            if (!this.isPathSafe(resolvedPath)) {
                throw new Error('Access denied: Path is outside allowed directory');
            }
            const results = [];
            const regex = new RegExp(pattern, ignoreCase ? 'gi' : 'g');
            if (recursive) {
                const files = await (0, glob_1.glob)('**/*', {
                    cwd: resolvedPath,
                    nodir: true,
                    maxDepth: 10,
                });
                for (const file of files.slice(0, 50)) { // Limit files to search
                    const fullPath = path_1.default.join(resolvedPath, file);
                    if (await this.isTextFile(fullPath)) {
                        const matches = await this.searchInFile(fullPath, regex, lineNumbers);
                        if (matches.length > 0) {
                            results.push(`${file}:`);
                            results.push(...matches);
                            results.push('');
                        }
                    }
                    if (results.length > maxResults)
                        break;
                }
            }
            else {
                const stats = await promises_1.default.stat(resolvedPath);
                if (stats.isFile()) {
                    if (await this.isTextFile(resolvedPath)) {
                        const matches = await this.searchInFile(resolvedPath, regex, lineNumbers);
                        results.push(...matches);
                    }
                }
                else {
                    throw new Error('Path is a directory. Use recursive flag to search in directories.');
                }
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: results.length > 0
                    ? results.join('\n')
                    : 'No matches found',
                executionTime: Date.now() - startTime,
                metadata: {
                    pattern,
                    path: resolvedPath,
                    recursive,
                    ignoreCase,
                    lineNumbers,
                    matchCount: results.length,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async searchInFile(filePath, regex, showLineNumbers) {
        try {
            const content = await promises_1.default.readFile(filePath, 'utf-8');
            const lines = content.split('\n');
            const matches = [];
            lines.forEach((line, index) => {
                if (regex.test(line)) {
                    const lineNumber = showLineNumbers ? `${index + 1}:` : '';
                    matches.push(`${lineNumber}${line}`);
                }
            });
            return matches;
        }
        catch (error) {
            return [];
        }
    }
    async getFileInfo(filePath) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const resolvedPath = path_1.default.resolve(filePath);
            // Security check
            if (!this.isPathSafe(resolvedPath)) {
                throw new Error('Access denied: Path is outside allowed directory');
            }
            const stats = await promises_1.default.stat(resolvedPath);
            const isText = await this.isTextFile(resolvedPath);
            const info = {
                path: resolvedPath,
                type: stats.isDirectory() ? 'directory' : 'file',
                size: this.formatBytes(stats.size),
                created: stats.birthtime.toISOString(),
                modified: stats.mtime.toISOString(),
                accessed: stats.atime.toISOString(),
                permissions: stats.mode.toString(8),
                isText,
            };
            return {
                id: resultId,
                toolCallId: '',
                success: true,
                output: JSON.stringify(info, null, 2),
                executionTime: Date.now() - startTime,
                metadata: info,
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async isTextFile(filePath) {
        try {
            const buffer = await promises_1.default.readFile(filePath);
            const sample = buffer.subarray(0, Math.min(1024, buffer.length));
            // Check for null bytes (common in binary files)
            for (let i = 0; i < sample.length; i++) {
                if (sample[i] === 0) {
                    return false;
                }
            }
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async listDirectory(dirPath, options = {}) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const resolvedPath = path_1.default.resolve(dirPath);
            // Security check
            if (!this.isPathSafe(resolvedPath)) {
                throw new Error('Access denied: Path is outside allowed directory');
            }
            const stats = await promises_1.default.stat(resolvedPath);
            if (!stats.isDirectory()) {
                throw new Error('Path is not a directory');
            }
            const entries = await this.getDirectoryEntries(resolvedPath, options);
            const output = entries.map(entry => {
                const type = entry.isDirectory ? 'd' : 'f';
                const size = entry.isDirectory ? '' : this.formatBytes(entry.size || 0);
                const modified = entry.modified?.toLocaleDateString() || '';
                return `${type} ${entry.name.padEnd(30)} ${size.padEnd(10)} ${modified}`;
            }).join('\n');
            return {
                id: resultId,
                toolCallId: '',
                success: true,
                output: `Directory listing for ${resolvedPath}:\n\nType Name${' '.repeat(26)} Size       Modified\n${'='.repeat(60)}\n${output}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    entryCount: entries.length,
                    recursive: options.recursive,
                    showHidden: options.showHidden,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async getDirectoryEntries(dirPath, options, depth = 0) {
        const entries = [];
        const items = await promises_1.default.readdir(dirPath, { withFileTypes: true });
        for (const item of items) {
            // Skip hidden files unless requested
            if (!options.showHidden && item.name.startsWith('.')) {
                continue;
            }
            const fullPath = path_1.default.join(dirPath, item.name);
            const stats = await promises_1.default.stat(fullPath);
            const indent = '  '.repeat(depth);
            entries.push({
                name: `${indent}${item.name}`,
                isDirectory: item.isDirectory(),
                size: item.isFile() ? stats.size : undefined,
                modified: stats.mtime,
            });
            // Recursively process subdirectories if requested and depth limit not exceeded
            if (options.recursive && item.isDirectory() && depth < 5) {
                const subEntries = await this.getDirectoryEntries(fullPath, options, depth + 1);
                entries.push(...subEntries);
            }
        }
        return entries;
    }
    async watch(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const watchPath = args.path;
            const recursive = args.recursive;
            if (!watchPath) {
                throw new Error('Watch path is required');
            }
            const resolvedPath = path_1.default.resolve(watchPath);
            // Security check
            if (!this.isPathSafe(resolvedPath)) {
                throw new Error('Access denied: Path is outside allowed directory');
            }
            // Stop existing watcher if any
            await this.stopWatch(resolvedPath);
            // Create new watcher
            const watcher = chokidar_1.default.watch(resolvedPath, {
                ignoreInitial: true,
                persistent: true,
                depth: recursive ? undefined : 0,
            });
            // Set up event handlers
            watcher
                .on('add', (filePath) => {
                this.emit('file-added', { path: filePath, type: 'file' });
                logger_1.logger.debug('File added', { path: filePath });
            })
                .on('change', (filePath) => {
                this.emit('file-changed', { path: filePath, type: 'file' });
                logger_1.logger.debug('File changed', { path: filePath });
            })
                .on('unlink', (filePath) => {
                this.emit('file-removed', { path: filePath, type: 'file' });
                logger_1.logger.debug('File removed', { path: filePath });
            })
                .on('addDir', (dirPath) => {
                this.emit('directory-added', { path: dirPath, type: 'directory' });
                logger_1.logger.debug('Directory added', { path: dirPath });
            })
                .on('unlinkDir', (dirPath) => {
                this.emit('directory-removed', { path: dirPath, type: 'directory' });
                logger_1.logger.debug('Directory removed', { path: dirPath });
            })
                .on('error', (error) => {
                this.emit('watch-error', { path: resolvedPath, error });
                logger_1.logger.error('File watcher error', error);
            });
            this.watchers.set(resolvedPath, watcher);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `Started watching ${resolvedPath} (recursive: ${recursive})`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    recursive,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async stopWatch(watchPath) {
        const resolvedPath = path_1.default.resolve(watchPath);
        const watcher = this.watchers.get(resolvedPath);
        if (watcher) {
            await watcher.close();
            this.watchers.delete(resolvedPath);
        }
    }
    async stopAllWatchers() {
        for (const [, watcher] of this.watchers) {
            await watcher.close();
        }
        this.watchers.clear();
    }
}
exports.FileTool = FileTool;
//# sourceMappingURL=files.js.map