import type { ToolResult } from '../types';
export declare class SystemTool {
    getInfo(args: Record<string, unknown>): Promise<ToolResult>;
    private getBasicInfo;
    private getDetailedInfo;
    private getProcessInfo;
    private getNetworkInfo;
    private getSystemProcesses;
    private parseProcessOutput;
    private getNetworkStats;
    private formatInfo;
    private formatBytes;
    private formatUptime;
    private parseMemoryString;
    listProcesses(args: Record<string, unknown>): Promise<ToolResult>;
    getEnvVar(args: Record<string, unknown>): Promise<ToolResult>;
    setEnvVar(args: Record<string, unknown>): Promise<ToolResult>;
}
//# sourceMappingURL=system.d.ts.map