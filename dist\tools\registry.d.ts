import { EventEmitter } from 'events';
import type { Tool<PERSON>all, ToolResult } from '../types';
export interface Tool<PERSON>hain {
    id: string;
    tools: ToolCall[];
    parallel?: boolean;
    continueOnError?: boolean;
}
export interface ChainResult {
    id: string;
    chainId: string;
    results: ToolResult[];
    success: boolean;
    executionTime: number;
}
export interface Tool {
    name: string;
    description: string;
    parameters: any;
    execute: (args: Record<string, unknown>) => Promise<ToolResult>;
}
export declare class ToolRegistry extends EventEmitter {
    private tools;
    private shellTool;
    private fileTool;
    private systemTool;
    constructor();
    initialize(): Promise<void>;
    private registerBuiltinTools;
    registerTool(tool: Tool): void;
    unregisterTool(name: string): boolean;
    getTool(name: string): Tool | undefined;
    getAvailableTools(): any[];
    executeTool(toolCall: ToolCall): Promise<ToolResult>;
    executeToolChain(chain: ToolChain): Promise<ChainResult>;
    executeToolsInParallel(toolCalls: ToolCall[]): Promise<ToolResult[]>;
    createToolChain(tools: ToolCall[], options?: {
        parallel?: boolean;
        continueOnError?: boolean;
    }): ToolChain;
    shutdown(): Promise<void>;
}
//# sourceMappingURL=registry.d.ts.map