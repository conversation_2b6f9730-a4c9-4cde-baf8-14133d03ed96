import { EventEmitter } from 'events';
import { nanoid } from 'nanoid';
import { logger } from '@/utils/logger';
import { ShellTool } from './shell';
import { FileTool } from './files';
import { SystemTool } from './system';
import type { Tool<PERSON>all, ToolResult } from '@/types';

export interface ToolChain {
  id: string;
  tools: ToolCall[];
  parallel?: boolean;
  continueOnError?: boolean;
}

export interface ChainResult {
  id: string;
  chainId: string;
  results: ToolResult[];
  success: boolean;
  executionTime: number;
}

export interface Tool {
  name: string;
  description: string;
  parameters: any;
  execute: (args: Record<string, unknown>) => Promise<ToolResult>;
}

export class ToolRegistry extends EventEmitter {
  private tools: Map<string, Tool> = new Map();
  private shellTool: ShellTool;
  private fileTool: FileTool;
  private systemTool: SystemTool;

  constructor() {
    super();
    
    this.shellTool = new ShellTool();
    this.fileTool = new FileTool();
    this.systemTool = new SystemTool();
  }

  async initialize(): Promise<void> {
    try {
      await this.registerBuiltinTools();
      logger.info('Tool registry initialized', { toolCount: this.tools.size });
    } catch (error) {
      logger.error('Failed to initialize tool registry', error);
      throw error;
    }
  }

  private async registerBuiltinTools(): Promise<void> {
    // Shell command execution
    this.registerTool({
      name: 'shell',
      description: 'Execute shell commands safely. Use for running terminal commands, scripts, and system operations.',
      parameters: {
        type: 'object',
        properties: {
          command: {
            type: 'string',
            description: 'The shell command to execute',
          },
          args: {
            type: 'array',
            items: { type: 'string' },
            description: 'Command arguments as an array',
            default: [],
          },
          cwd: {
            type: 'string',
            description: 'Working directory for the command',
          },
          timeout: {
            type: 'number',
            description: 'Timeout in milliseconds',
            default: 30000,
          },
          env: {
            type: 'object',
            description: 'Environment variables for the command',
            additionalProperties: { type: 'string' },
          },
          background: {
            type: 'boolean',
            description: 'Run command in background',
            default: false,
          },
        },
        required: ['command'],
      },
      execute: async (args) => {
        return await this.shellTool.execute(args);
      },
    });

    // File reading
    this.registerTool({
      name: 'file_read',
      description: 'Read the contents of a file. Use for examining code, configuration files, or any text files.',
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Path to the file to read',
          },
          encoding: {
            type: 'string',
            description: 'File encoding',
            default: 'utf-8',
          },
          lines: {
            type: 'number',
            description: 'Number of lines to read from the beginning',
          },
          offset: {
            type: 'number',
            description: 'Line offset to start reading from',
          },
          maxSize: {
            type: 'number',
            description: 'Maximum file size to read in bytes',
            default: 52428800,
          },
        },
        required: ['path'],
      },
      execute: async (args) => {
        return await this.fileTool.read(args);
      },
    });

    // File writing
    this.registerTool({
      name: 'file_write',
      description: 'Write content to a file. Use for creating or updating files with new content.',
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Path to the file to write',
          },
          content: {
            type: 'string',
            description: 'Content to write to the file',
          },
          encoding: {
            type: 'string',
            description: 'File encoding',
            default: 'utf-8',
          },
          backup: {
            type: 'boolean',
            description: 'Create a backup before writing',
            default: false,
          },
        },
        required: ['path', 'content'],
      },
      execute: async (args) => {
        return await this.fileTool.write(args);
      },
    });

    // File creation
    this.registerTool({
      name: 'file_create',
      description: 'Create a new file with content. Use for creating new files that don\'t exist.',
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Path to the new file',
          },
          content: {
            type: 'string',
            description: 'Initial content for the file',
            default: '',
          },
          encoding: {
            type: 'string',
            description: 'File encoding',
            default: 'utf-8',
          },
        },
        required: ['path'],
      },
      execute: async (args) => {
        return await this.fileTool.create(args);
      },
    });

    // File deletion
    this.registerTool({
      name: 'file_delete',
      description: 'Delete a file or directory. Use with caution for removing files.',
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Path to the file or directory to delete',
          },
          recursive: {
            type: 'boolean',
            description: 'Delete directories recursively',
            default: false,
          },
          force: {
            type: 'boolean',
            description: 'Force deletion without confirmation',
            default: false,
          },
        },
        required: ['path'],
      },
      execute: async (args) => {
        return await this.fileTool.delete(args);
      },
    });

    // File moving/renaming
    this.registerTool({
      name: 'file_move',
      description: 'Move or rename a file or directory.',
      parameters: {
        type: 'object',
        properties: {
          source: {
            type: 'string',
            description: 'Source path',
          },
          destination: {
            type: 'string',
            description: 'Destination path',
          },
        },
        required: ['source', 'destination'],
      },
      execute: async (args) => {
        return await this.fileTool.move(args);
      },
    });

    // File copying
    this.registerTool({
      name: 'file_copy',
      description: 'Copy a file or directory to a new location.',
      parameters: {
        type: 'object',
        properties: {
          source: {
            type: 'string',
            description: 'Source path',
          },
          destination: {
            type: 'string',
            description: 'Destination path',
          },
          recursive: {
            type: 'boolean',
            description: 'Copy directories recursively',
            default: false,
          },
        },
        required: ['source', 'destination'],
      },
      execute: async (args) => {
        return await this.fileTool.copy(args);
      },
    });

    // File searching with glob patterns
    this.registerTool({
      name: 'file_search',
      description: 'Search for files using glob patterns. Use for finding files by name or pattern.',
      parameters: {
        type: 'object',
        properties: {
          pattern: {
            type: 'string',
            description: 'Glob pattern to search for',
          },
          cwd: {
            type: 'string',
            description: 'Directory to search in',
          },
          maxDepth: {
            type: 'number',
            description: 'Maximum search depth',
            default: 10,
          },
        },
        required: ['pattern'],
      },
      execute: async (args) => {
        return await this.fileTool.search(args);
      },
    });

    // Text searching with grep
    this.registerTool({
      name: 'grep',
      description: 'Search for text patterns in files. Use for finding specific content in files.',
      parameters: {
        type: 'object',
        properties: {
          pattern: {
            type: 'string',
            description: 'Text pattern to search for',
          },
          files: {
            type: 'array',
            items: { type: 'string' },
            description: 'Files to search in',
          },
          recursive: {
            type: 'boolean',
            description: 'Search recursively in directories',
            default: false,
          },
          ignoreCase: {
            type: 'boolean',
            description: 'Case-insensitive search',
            default: false,
          },
          lineNumbers: {
            type: 'boolean',
            description: 'Show line numbers',
            default: true,
          },
        },
        required: ['pattern', 'files'],
      },
      execute: async (args) => {
        return await this.fileTool.grep(args);
      },
    });

    // File information
    this.registerTool({
      name: 'file_info',
      description: 'Get detailed information about a file or directory including size, permissions, and metadata.',
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Path to the file or directory',
          },
        },
        required: ['path'],
      },
      execute: async (args) => {
        return await this.fileTool.getFileInfo(args.path as string);
      },
    });

    // Directory listing
    this.registerTool({
      name: 'list_directory',
      description: 'List contents of a directory with detailed information.',
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Path to the directory',
            default: '.',
          },
          recursive: {
            type: 'boolean',
            description: 'List subdirectories recursively',
            default: false,
          },
          showHidden: {
            type: 'boolean',
            description: 'Show hidden files and directories',
            default: false,
          },
        },
      },
      execute: async (args) => {
        return await this.fileTool.listDirectory(args.path as string || '.', {
          recursive: args.recursive as boolean,
          showHidden: args.showHidden as boolean,
        });
      },
    });

    // System information
    this.registerTool({
      name: 'system_info',
      description: 'Get system information including OS, hardware, and environment details.',
      parameters: {
        type: 'object',
        properties: {
          type: {
            type: 'string',
            enum: ['basic', 'detailed', 'processes', 'network'],
            description: 'Type of system information to retrieve',
            default: 'basic',
          },
        },
      },
      execute: async (args) => {
        return await this.systemTool.getInfo(args);
      },
    });

    // Advanced file operations
    this.registerTool({
      name: 'file_watch',
      description: 'Watch a file or directory for changes.',
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Path to watch for changes',
          },
          recursive: {
            type: 'boolean',
            description: 'Watch subdirectories recursively',
            default: false,
          },
          events: {
            type: 'array',
            items: { type: 'string', enum: ['add', 'change', 'unlink', 'addDir', 'unlinkDir'] },
            description: 'Events to watch for',
            default: ['change'],
          },
        },
        required: ['path'],
      },
      execute: async (args) => {
        return await this.fileTool.watch(args);
      },
    });

    // Process management
    this.registerTool({
      name: 'process_list',
      description: 'List running processes with detailed information.',
      parameters: {
        type: 'object',
        properties: {
          filter: {
            type: 'string',
            description: 'Filter processes by name or command',
          },
          sortBy: {
            type: 'string',
            enum: ['pid', 'name', 'cpu', 'memory'],
            description: 'Sort processes by field',
            default: 'pid',
          },
          limit: {
            type: 'number',
            description: 'Maximum number of processes to return',
            default: 50,
          },
        },
      },
      execute: async (args) => {
        return await this.systemTool.listProcesses(args);
      },
    });

    // Environment management
    this.registerTool({
      name: 'env_get',
      description: 'Get environment variable value.',
      parameters: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'Environment variable name',
          },
          default: {
            type: 'string',
            description: 'Default value if variable is not set',
          },
        },
        required: ['name'],
      },
      execute: async (args) => {
        return await this.systemTool.getEnvVar(args);
      },
    });

    this.registerTool({
      name: 'env_set',
      description: 'Set environment variable for the current session.',
      parameters: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'Environment variable name',
          },
          value: {
            type: 'string',
            description: 'Environment variable value',
          },
        },
        required: ['name', 'value'],
      },
      execute: async (args) => {
        return await this.systemTool.setEnvVar(args);
      },
    });
  }

  registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
    logger.debug('Tool registered', { name: tool.name });
  }

  unregisterTool(name: string): boolean {
    const removed = this.tools.delete(name);
    if (removed) {
      logger.debug('Tool unregistered', { name });
    }
    return removed;
  }

  getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  getAvailableTools(): any[] {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters,
    }));
  }

  async executeTool(toolCall: ToolCall): Promise<ToolResult> {
    const startTime = Date.now();
    const tool = this.tools.get(toolCall.name);

    if (!tool) {
      return {
        id: nanoid(),
        toolCallId: toolCall.id,
        success: false,
        output: '',
        error: `Tool not found: ${toolCall.name}`,
        executionTime: Date.now() - startTime,
      };
    }

    try {
      logger.debug('Executing tool', { 
        name: toolCall.name, 
        args: toolCall.arguments 
      });

      const result = await tool.execute(toolCall.arguments);
      
      logger.debug('Tool execution completed', { 
        name: toolCall.name, 
        success: result.success,
        executionTime: result.executionTime 
      });

      this.emit('tool-executed', result);
      return result;

    } catch (error) {
      const errorResult: ToolResult = {
        id: nanoid(),
        toolCallId: toolCall.id,
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };

      logger.error('Tool execution failed', { 
        name: toolCall.name, 
        error: errorResult.error 
      });

      this.emit('tool-error', { toolCall, error: errorResult });
      return errorResult;
    }
  }

  async executeToolChain(chain: ToolChain): Promise<ChainResult> {
    const startTime = Date.now();
    const results: ToolResult[] = [];
    let success = true;

    logger.info('Executing tool chain', {
      chainId: chain.id,
      toolCount: chain.tools.length,
      parallel: chain.parallel
    });

    try {
      if (chain.parallel) {
        // Execute tools in parallel
        const promises = chain.tools.map(toolCall => this.executeTool(toolCall));
        const parallelResults = await Promise.allSettled(promises);

        for (const result of parallelResults) {
          if (result.status === 'fulfilled') {
            results.push(result.value);
            if (!result.value.success) {
              success = false;
            }
          } else {
            success = false;
            results.push({
              id: nanoid(),
              toolCallId: '',
              success: false,
              output: '',
              error: result.reason?.message || 'Unknown error',
              executionTime: 0,
            });
          }
        }
      } else {
        // Execute tools sequentially
        for (const toolCall of chain.tools) {
          const result = await this.executeTool(toolCall);
          results.push(result);

          if (!result.success) {
            success = false;
            if (!chain.continueOnError) {
              break;
            }
          }
        }
      }

      const chainResult: ChainResult = {
        id: nanoid(),
        chainId: chain.id,
        results,
        success,
        executionTime: Date.now() - startTime,
      };

      this.emit('chain-executed', chainResult);
      logger.info('Tool chain execution completed', {
        chainId: chain.id,
        success,
        executionTime: chainResult.executionTime
      });

      return chainResult;
    } catch (error) {
      logger.error('Tool chain execution failed', error);
      throw error;
    }
  }

  async executeToolsInParallel(toolCalls: ToolCall[]): Promise<ToolResult[]> {
    const promises = toolCalls.map(toolCall => this.executeTool(toolCall));
    const results = await Promise.allSettled(promises);

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          id: nanoid(),
          toolCallId: toolCalls[index].id,
          success: false,
          output: '',
          error: result.reason?.message || 'Unknown error',
          executionTime: 0,
        };
      }
    });
  }

  createToolChain(tools: ToolCall[], options: { parallel?: boolean; continueOnError?: boolean } = {}): ToolChain {
    return {
      id: nanoid(),
      tools,
      parallel: options.parallel || false,
      continueOnError: options.continueOnError || false,
    };
  }

  async shutdown(): Promise<void> {
    try {
      await this.fileTool.stopAllWatchers();
      this.tools.clear();
      this.removeAllListeners();
      logger.info('Tool registry shutdown');
    } catch (error) {
      logger.error('Error during tool registry shutdown', error);
    }
  }
}
