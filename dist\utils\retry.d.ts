export interface RetryOptions {
    maxAttempts: number;
    baseDelay: number;
    maxDelay: number;
    exponentialBackoff: boolean;
    jitter: boolean;
    retryCondition?: (error: Error) => boolean;
    onRetry?: (error: Error, attempt: number) => void;
}
export interface RetryResult<T> {
    success: boolean;
    result?: T;
    error?: Error;
    attempts: number;
    totalTime: number;
}
export declare class RetryManager {
    private static defaultOptions;
    static retry<T>(operation: () => Promise<T>, options?: Partial<RetryOptions>): Promise<RetryResult<T>>;
    static retryWithCircuitBreaker<T>(operation: () => Promise<T>, options?: Partial<RetryOptions> & {
        circuitBreakerThreshold?: number;
        circuitBreakerTimeout?: number;
    }): Promise<RetryResult<T>>;
    private static sleep;
    static isRetryableError(error: Error): boolean;
    static isRateLimitError(error: Error): boolean;
}
declare class CircuitBreaker {
    private threshold;
    private timeout;
    private failures;
    private lastFailureTime;
    private state;
    constructor(threshold: number, timeout: number);
    isOpen(): boolean;
    recordSuccess(): void;
    recordFailure(): void;
}
export { CircuitBreaker };
//# sourceMappingURL=retry.d.ts.map