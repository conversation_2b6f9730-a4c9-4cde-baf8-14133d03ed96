"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProviderManager = void 0;
const openai_1 = __importDefault(require("openai"));
const nanoid_1 = require("nanoid");
const logger_1 = require("../utils/logger");
const retry_1 = require("../utils/retry");
class AIProviderManager {
    client;
    config;
    constructor(config) {
        this.config = {
            provider: config.provider,
            model: config.model,
            apiKey: config.apiKey,
            baseUrl: config.baseUrl,
            maxTokens: config.maxTokens || 4096,
            temperature: config.temperature || 0.7,
        };
        this.client = this.createClient();
    }
    createClient() {
        const clientConfig = {
            apiKey: this.config.apiKey || process.env['OPENAI_API_KEY'],
        };
        // Configure for different providers
        switch (this.config.provider) {
            case 'openai':
                // Default OpenAI configuration
                break;
            case 'deepseek':
                clientConfig.baseURL = this.config.baseUrl || 'https://api.deepseek.com/v1';
                clientConfig.apiKey = this.config.apiKey || process.env['DEEPSEEK_API_KEY'];
                break;
            case 'ollama':
                clientConfig.baseURL = this.config.baseUrl || 'http://localhost:11434/v1';
                clientConfig.apiKey = 'ollama'; // Ollama doesn't require a real API key
                break;
            case 'azure':
                clientConfig.baseURL = this.config.baseUrl;
                clientConfig.apiKey = this.config.apiKey || process.env['AZURE_OPENAI_API_KEY'];
                clientConfig.defaultHeaders = {
                    'api-key': clientConfig.apiKey,
                };
                break;
            default:
                throw new Error(`Unsupported AI provider: ${this.config.provider}`);
        }
        return new openai_1.default(clientConfig);
    }
    async updateConfig(newConfig) {
        this.config = {
            provider: newConfig.provider,
            model: newConfig.model,
            apiKey: newConfig.apiKey,
            baseUrl: newConfig.baseUrl,
            maxTokens: newConfig.maxTokens || this.config.maxTokens,
            temperature: newConfig.temperature || this.config.temperature,
        };
        this.client = this.createClient();
        logger_1.logger.info('AI provider config updated', {
            provider: this.config.provider,
            model: this.config.model
        });
    }
    async streamCompletion(options) {
        const { messages, tools, context, onChunk, onToolCall } = options;
        // Prepare system message with context
        const systemMessage = this.buildSystemMessage(context);
        // Convert messages to OpenAI format
        const openaiMessages = this.convertMessages([systemMessage, ...messages]);
        // Prepare tools for OpenAI format
        const openaiTools = this.convertTools(tools);
        const result = await retry_1.RetryManager.retry(async () => {
            const stream = await this.client.chat.completions.create({
                model: this.config.model,
                messages: openaiMessages,
                tools: openaiTools.length > 0 ? openaiTools : undefined,
                tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
                max_tokens: this.config.maxTokens || null,
                temperature: this.config.temperature || null,
                stream: true,
            });
            return stream;
        }, {
            maxAttempts: 3,
            baseDelay: 1000,
            exponentialBackoff: true,
            retryCondition: (error) => retry_1.RetryManager.isRetryableError(error),
            onRetry: (error, attempt) => {
                logger_1.logger.warn('Retrying AI completion', {
                    attempt,
                    error: error.message,
                    provider: this.config.provider
                });
                onChunk?.({
                    type: 'error',
                    content: `Retrying request (attempt ${attempt})...`,
                    metadata: { error: error.message },
                });
            },
        });
        if (!result.success || !result.result) {
            throw result.error || new Error('Failed to get AI response');
        }
        const stream = result.result;
        try {
            let assistantMessage = '';
            const toolCalls = [];
            const toolResults = [];
            let currentToolCall = null;
            for await (const chunk of stream) {
                const delta = chunk.choices[0]?.delta;
                if (delta?.content) {
                    assistantMessage += delta.content;
                    onChunk?.({
                        type: 'text',
                        content: delta.content,
                    });
                }
                if (delta?.tool_calls) {
                    for (const toolCallDelta of delta.tool_calls) {
                        if (toolCallDelta.index !== undefined) {
                            if (currentToolCall && currentToolCall.id) {
                                // Finish previous tool call
                                toolCalls.push(currentToolCall);
                            }
                            // Start new tool call
                            currentToolCall = {
                                id: toolCallDelta.id || (0, nanoid_1.nanoid)(),
                                name: toolCallDelta.function?.name || '',
                                arguments: {},
                                riskLevel: 'safe',
                            };
                        }
                        if (toolCallDelta.function?.name && currentToolCall) {
                            currentToolCall.name = toolCallDelta.function.name;
                        }
                        if (toolCallDelta.function?.arguments && currentToolCall) {
                            try {
                                const args = JSON.parse(toolCallDelta.function.arguments);
                                currentToolCall.arguments = { ...currentToolCall.arguments, ...args };
                            }
                            catch (error) {
                                // Partial JSON, continue accumulating
                            }
                        }
                    }
                }
                if (chunk.choices[0]?.finish_reason === 'tool_calls' && currentToolCall?.id) {
                    toolCalls.push(currentToolCall);
                    currentToolCall = null;
                }
            }
            // Execute tool calls if any
            if (toolCalls.length > 0 && onToolCall) {
                for (const toolCall of toolCalls) {
                    onChunk?.({
                        type: 'tool_call',
                        content: `Executing ${toolCall.name}...`,
                        metadata: { toolCall },
                    });
                    const result = await onToolCall(toolCall);
                    toolResults.push(result);
                    onChunk?.({
                        type: 'tool_result',
                        content: result.success ? result.output : `Error: ${result.error}`,
                        metadata: { result },
                    });
                }
                // If we have tool results, make another call to get the final response
                if (toolResults.length > 0) {
                    const toolMessage = {
                        id: (0, nanoid_1.nanoid)(),
                        role: 'assistant',
                        content: assistantMessage,
                        timestamp: new Date(),
                        toolCalls,
                        toolResults,
                    };
                    // Add tool result messages
                    const toolResultMessages = toolResults.map(result => ({
                        id: (0, nanoid_1.nanoid)(),
                        role: 'tool',
                        content: result.success ? result.output : `Error: ${result.error}`,
                        timestamp: new Date(),
                    }));
                    // Get final response
                    const finalResponse = await this.getFinalResponse([
                        ...openaiMessages,
                        this.convertMessage(toolMessage),
                        ...toolResultMessages.map(msg => this.convertMessage(msg)),
                    ]);
                    return {
                        id: (0, nanoid_1.nanoid)(),
                        role: 'assistant',
                        content: finalResponse,
                        timestamp: new Date(),
                        toolCalls,
                        toolResults,
                    };
                }
            }
            return {
                id: (0, nanoid_1.nanoid)(),
                role: 'assistant',
                content: assistantMessage,
                timestamp: new Date(),
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
                toolResults: toolResults.length > 0 ? toolResults : undefined,
            };
        }
        catch (error) {
            logger_1.logger.error('Error in AI completion', error);
            throw error;
        }
    }
    async getFinalResponse(messages) {
        const response = await this.client.chat.completions.create({
            model: this.config.model,
            messages,
            max_tokens: this.config.maxTokens || null,
            temperature: this.config.temperature || null,
        });
        return response.choices[0]?.message?.content || '';
    }
    buildSystemMessage(context) {
        const { projectStructure, environmentInfo } = context;
        const systemPrompt = `You are Kritrima, an advanced AI-powered CLI assistant with agentic capabilities. You can execute shell commands, manipulate files, and help with development tasks.

CURRENT CONTEXT:
- Working Directory: ${projectStructure.root}
- Project Type: ${projectStructure.type}
- Language: ${projectStructure.language || 'Unknown'}
- Framework: ${projectStructure.framework || 'None'}
- Package Manager: ${projectStructure.packageManager || 'None'}
- Platform: ${environmentInfo.platform}
- Node Version: ${environmentInfo.nodeVersion}

CAPABILITIES:
- Execute shell commands safely
- Read, write, create, delete, move, and copy files
- Search files with grep and glob patterns
- Analyze project structure and dependencies
- Install packages and manage dependencies
- Run tests and build processes
- Git operations
- System information gathering

SAFETY GUIDELINES:
- Always assess risk before executing commands
- Ask for confirmation for destructive operations
- Prefer safe, reversible operations
- Provide clear explanations of what you're doing
- Use appropriate tools for each task

AVAILABLE TOOLS:
- shell: Execute shell commands
- file_read: Read file contents
- file_write: Write content to files
- file_create: Create new files
- file_delete: Delete files
- file_move: Move/rename files
- file_copy: Copy files
- file_search: Search files with patterns
- grep: Search text in files
- system_info: Get system information

Be helpful, accurate, and safe in all operations.`;
        return {
            id: (0, nanoid_1.nanoid)(),
            role: 'system',
            content: systemPrompt,
            timestamp: new Date(),
        };
    }
    convertMessages(messages) {
        return messages.map(msg => this.convertMessage(msg));
    }
    convertMessage(message) {
        const converted = {
            role: message.role,
            content: message.content,
        };
        if (message.toolCalls) {
            converted.tool_calls = message.toolCalls.map(tc => ({
                id: tc.id,
                type: 'function',
                function: {
                    name: tc.name,
                    arguments: JSON.stringify(tc.arguments),
                },
            }));
        }
        return converted;
    }
    convertTools(tools) {
        return tools.map(tool => ({
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: tool.parameters,
            },
        }));
    }
    async testConnection() {
        try {
            const response = await this.client.chat.completions.create({
                model: this.config.model,
                messages: [{ role: 'user', content: 'Hello' }],
                max_tokens: 10,
            });
            return !!response.choices[0]?.message?.content;
        }
        catch (error) {
            logger_1.logger.error('AI provider connection test failed', error);
            return false;
        }
    }
    getConfig() {
        return { ...this.config };
    }
}
exports.AIProviderManager = AIProviderManager;
//# sourceMappingURL=providers.js.map