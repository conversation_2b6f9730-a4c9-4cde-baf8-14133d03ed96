import fs from 'fs/promises';
import path from 'path';
import { glob } from 'glob';
import { nanoid } from 'nanoid';
import { logger } from '@/utils/logger';
import chokidar, { FSWatcher } from 'chokidar';
import { EventEmitter } from 'events';
import type { ToolResult } from '@/types';

export class FileTool extends EventEmitter {
  private readonly maxFileSize = 50 * 1024 * 1024; // 50MB default
  private readonly backupDir = '.kritrima-backups';
  private watchers = new Map<string, FSWatcher>();

  constructor() {
    super();
  }

  async read(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const filePath = args.path as string;
      const encoding = (args.encoding as string) || 'utf-8';
      const lines = args.lines as number;
      const offset = args.offset as number;

      if (!filePath) {
        throw new Error('File path is required');
      }

      const resolvedPath = path.resolve(filePath);

      // Security check - prevent reading outside working directory
      if (!this.isPathSafe(resolvedPath)) {
        throw new Error('Access denied: Path is outside allowed directory');
      }

      // Check if file exists
      await fs.access(resolvedPath);

      // Get file stats
      const stats = await fs.stat(resolvedPath);
      if (stats.isDirectory()) {
        throw new Error('Path is a directory, not a file');
      }

      // Check file size
      if (stats.size > this.maxFileSize) {
        throw new Error(`File too large (${this.formatBytes(stats.size)}). Maximum allowed: ${this.formatBytes(this.maxFileSize)}`);
      }

      let content: string;

      // Handle partial reading if lines or offset specified
      if (lines !== undefined || offset !== undefined) {
        content = await this.readFilePartial(resolvedPath, encoding as BufferEncoding, lines, offset);
      } else {
        content = await fs.readFile(resolvedPath, { encoding: encoding as BufferEncoding });
      }

      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: true,
        output: content,
        executionTime: Date.now() - startTime,
        metadata: {
          path: resolvedPath,
          size: stats.size,
          encoding,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async write(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const filePath = args.path as string;
      const content = args.content as string;
      const encoding = (args.encoding as string) || 'utf-8';
      const backup = args.backup as boolean;

      if (!filePath) {
        throw new Error('File path is required');
      }

      if (content === undefined) {
        throw new Error('Content is required');
      }

      const resolvedPath = path.resolve(filePath);

      // Security check
      if (!this.isPathSafe(resolvedPath)) {
        throw new Error('Access denied: Path is outside allowed directory');
      }

      // Create backup if requested and file exists
      if (backup) {
        try {
          await fs.access(resolvedPath);
          await this.createBackup(resolvedPath);
        } catch (error) {
          // File doesn't exist, no backup needed
        }
      }

      // Ensure directory exists
      const dir = path.dirname(resolvedPath);
      await fs.mkdir(dir, { recursive: true });

      // Write file
      await fs.writeFile(resolvedPath, content, { encoding: encoding as BufferEncoding });

      const stats = await fs.stat(resolvedPath);

      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: true,
        output: `File written successfully: ${resolvedPath} (${stats.size} bytes)`,
        executionTime: Date.now() - startTime,
        metadata: {
          path: resolvedPath,
          size: stats.size,
          encoding,
          backup,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async create(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const filePath = args.path as string;
      const content = (args.content as string) || '';
      const encoding = (args.encoding as string) || 'utf-8';

      if (!filePath) {
        throw new Error('File path is required');
      }

      const resolvedPath = path.resolve(filePath);

      // Check if file already exists
      try {
        await fs.access(resolvedPath);
        throw new Error('File already exists');
      } catch (error: any) {
        // File doesn't exist, which is what we want
        if (error.code !== 'ENOENT') {
          throw error;
        }
      }

      // Ensure directory exists
      const dir = path.dirname(resolvedPath);
      await fs.mkdir(dir, { recursive: true });

      // Create file
      await fs.writeFile(resolvedPath, content, { encoding: encoding as BufferEncoding });

      const stats = await fs.stat(resolvedPath);

      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: true,
        output: `File created successfully: ${resolvedPath} (${stats.size} bytes)`,
        executionTime: Date.now() - startTime,
        metadata: {
          path: resolvedPath,
          size: stats.size,
          encoding,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  // Additional methods will be added in the next chunk
  private async readFilePartial(filePath: string, encoding: BufferEncoding, lines?: number, offset?: number): Promise<string> {
    const content = await fs.readFile(filePath, { encoding });
    const allLines = content.split('\n');
    
    const startLine = offset || 0;
    const endLine = lines ? startLine + lines : allLines.length;
    
    return allLines.slice(startLine, endLine).join('\n');
  }

  private isPathSafe(filePath: string): boolean {
    const cwd = process.cwd();
    const resolved = path.resolve(filePath);
    return resolved.startsWith(cwd);
  }

  private formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  async createBackup(filePath: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.backupDir, `${path.basename(filePath)}.${timestamp}.backup`);

    await fs.mkdir(this.backupDir, { recursive: true });
    await fs.copyFile(filePath, backupPath);

    return backupPath;
  }

  async delete(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const filePath = args.path as string;
      const force = args.force as boolean;
      const backup = args.backup as boolean;

      if (!filePath) {
        throw new Error('File path is required');
      }

      const resolvedPath = path.resolve(filePath);

      // Security check
      if (!this.isPathSafe(resolvedPath)) {
        throw new Error('Access denied: Path is outside allowed directory');
      }

      // Check if file exists
      await fs.access(resolvedPath);

      const stats = await fs.stat(resolvedPath);

      // Create backup if requested
      if (backup && stats.isFile()) {
        await this.createBackup(resolvedPath);
      }

      // Delete file or directory
      if (stats.isDirectory()) {
        if (force) {
          await fs.rm(resolvedPath, { recursive: true, force: true });
        } else {
          await fs.rmdir(resolvedPath);
        }
      } else {
        await fs.unlink(resolvedPath);
      }

      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: true,
        output: `${stats.isDirectory() ? 'Directory' : 'File'} deleted successfully: ${resolvedPath}`,
        executionTime: Date.now() - startTime,
        metadata: {
          path: resolvedPath,
          type: stats.isDirectory() ? 'directory' : 'file',
          backup,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async move(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const sourcePath = args.source as string;
      const destPath = args.destination as string;
      const backup = args.backup as boolean;

      if (!sourcePath || !destPath) {
        throw new Error('Source and destination paths are required');
      }

      const resolvedSource = path.resolve(sourcePath);
      const resolvedDest = path.resolve(destPath);

      // Security checks
      if (!this.isPathSafe(resolvedSource) || !this.isPathSafe(resolvedDest)) {
        throw new Error('Access denied: Path is outside allowed directory');
      }

      // Check if source exists
      await fs.access(resolvedSource);

      // Create backup of destination if it exists and backup is requested
      if (backup) {
        try {
          await fs.access(resolvedDest);
          await this.createBackup(resolvedDest);
        } catch (error) {
          // Destination doesn't exist, no backup needed
        }
      }

      // Ensure destination directory exists
      const destDir = path.dirname(resolvedDest);
      await fs.mkdir(destDir, { recursive: true });

      // Move file/directory
      await fs.rename(resolvedSource, resolvedDest);

      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: true,
        output: `Moved successfully: ${resolvedSource} → ${resolvedDest}`,
        executionTime: Date.now() - startTime,
        metadata: {
          source: resolvedSource,
          destination: resolvedDest,
          backup,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async copy(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const sourcePath = args.source as string;
      const destPath = args.destination as string;
      const recursive = args.recursive as boolean;
      const backup = args.backup as boolean;

      if (!sourcePath || !destPath) {
        throw new Error('Source and destination paths are required');
      }

      const resolvedSource = path.resolve(sourcePath);
      const resolvedDest = path.resolve(destPath);

      // Security checks
      if (!this.isPathSafe(resolvedSource) || !this.isPathSafe(resolvedDest)) {
        throw new Error('Access denied: Path is outside allowed directory');
      }

      // Check if source exists
      await fs.access(resolvedSource);
      const sourceStats = await fs.stat(resolvedSource);

      // Create backup of destination if it exists and backup is requested
      if (backup) {
        try {
          await fs.access(resolvedDest);
          await this.createBackup(resolvedDest);
        } catch (error) {
          // Destination doesn't exist, no backup needed
        }
      }

      // Ensure destination directory exists
      const destDir = path.dirname(resolvedDest);
      await fs.mkdir(destDir, { recursive: true });

      // Copy file or directory
      if (sourceStats.isDirectory()) {
        if (!recursive) {
          throw new Error('Cannot copy directory without recursive flag');
        }
        await this.copyDirectory(resolvedSource, resolvedDest);
      } else {
        await fs.copyFile(resolvedSource, resolvedDest);
      }

      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: true,
        output: `Copied successfully: ${resolvedSource} → ${resolvedDest}`,
        executionTime: Date.now() - startTime,
        metadata: {
          source: resolvedSource,
          destination: resolvedDest,
          type: sourceStats.isDirectory() ? 'directory' : 'file',
          recursive,
          backup,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  private async copyDirectory(source: string, dest: string): Promise<void> {
    await fs.mkdir(dest, { recursive: true });

    const entries = await fs.readdir(source, { withFileTypes: true });

    for (const entry of entries) {
      const sourcePath = path.join(source, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectory(sourcePath, destPath);
      } else {
        await fs.copyFile(sourcePath, destPath);
      }
    }
  }

  async search(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const pattern = args.pattern as string;
      const cwd = (args.cwd as string) || process.cwd();
      const maxDepth = (args.maxDepth as number) || 10;

      if (!pattern) {
        throw new Error('Search pattern is required');
      }

      const files = await glob(pattern, {
        cwd: path.resolve(cwd),
        maxDepth,
        dot: false,
      });

      const results = files.slice(0, 100); // Limit results for performance

      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: true,
        output: results.length > 0
          ? `Found ${results.length} files:\n${results.join('\n')}`
          : 'No files found matching the pattern',
        executionTime: Date.now() - startTime,
        metadata: {
          pattern,
          cwd,
          maxDepth,
          resultCount: results.length,
          results,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async grep(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const pattern = args.pattern as string;
      const filePath = args.path as string;
      const recursive = args.recursive as boolean;
      const ignoreCase = args.ignoreCase as boolean;
      const lineNumbers = args.lineNumbers as boolean;
      const maxResults = (args.maxResults as number) || 100;

      if (!pattern) {
        throw new Error('Search pattern is required');
      }

      if (!filePath) {
        throw new Error('File path is required');
      }

      const resolvedPath = path.resolve(filePath);

      // Security check
      if (!this.isPathSafe(resolvedPath)) {
        throw new Error('Access denied: Path is outside allowed directory');
      }

      const results: string[] = [];
      const regex = new RegExp(pattern, ignoreCase ? 'gi' : 'g');

      if (recursive) {
        const files = await glob('**/*', {
          cwd: resolvedPath,
          nodir: true,
          maxDepth: 10,
        });

        for (const file of files.slice(0, 50)) { // Limit files to search
          const fullPath = path.join(resolvedPath, file);
          if (await this.isTextFile(fullPath)) {
            const matches = await this.searchInFile(fullPath, regex, lineNumbers);
            if (matches.length > 0) {
              results.push(`${file}:`);
              results.push(...matches);
              results.push('');
            }
          }
          if (results.length > maxResults) break;
        }
      } else {
        const stats = await fs.stat(resolvedPath);
        if (stats.isFile()) {
          if (await this.isTextFile(resolvedPath)) {
            const matches = await this.searchInFile(resolvedPath, regex, lineNumbers);
            results.push(...matches);
          }
        } else {
          throw new Error('Path is a directory. Use recursive flag to search in directories.');
        }
      }

      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: true,
        output: results.length > 0
          ? results.join('\n')
          : 'No matches found',
        executionTime: Date.now() - startTime,
        metadata: {
          pattern,
          path: resolvedPath,
          recursive,
          ignoreCase,
          lineNumbers,
          matchCount: results.length,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  private async searchInFile(filePath: string, regex: RegExp, showLineNumbers: boolean): Promise<string[]> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      const matches: string[] = [];

      lines.forEach((line, index) => {
        if (regex.test(line)) {
          const lineNumber = showLineNumbers ? `${index + 1}:` : '';
          matches.push(`${lineNumber}${line}`);
        }
      });

      return matches;
    } catch (error) {
      return [];
    }
  }

  async getFileInfo(filePath: string): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const resolvedPath = path.resolve(filePath);

      // Security check
      if (!this.isPathSafe(resolvedPath)) {
        throw new Error('Access denied: Path is outside allowed directory');
      }

      const stats = await fs.stat(resolvedPath);
      const isText = await this.isTextFile(resolvedPath);

      const info = {
        path: resolvedPath,
        type: stats.isDirectory() ? 'directory' : 'file',
        size: this.formatBytes(stats.size),
        created: stats.birthtime.toISOString(),
        modified: stats.mtime.toISOString(),
        accessed: stats.atime.toISOString(),
        permissions: stats.mode.toString(8),
        isText,
      };

      return {
        id: resultId,
        toolCallId: '',
        success: true,
        output: JSON.stringify(info, null, 2),
        executionTime: Date.now() - startTime,
        metadata: info,
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  private async isTextFile(filePath: string): Promise<boolean> {
    try {
      const buffer = await fs.readFile(filePath);
      const sample = buffer.subarray(0, Math.min(1024, buffer.length));

      // Check for null bytes (common in binary files)
      for (let i = 0; i < sample.length; i++) {
        if (sample[i] === 0) {
          return false;
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  async listDirectory(dirPath: string, options: { recursive?: boolean; showHidden?: boolean } = {}): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const resolvedPath = path.resolve(dirPath);

      // Security check
      if (!this.isPathSafe(resolvedPath)) {
        throw new Error('Access denied: Path is outside allowed directory');
      }

      const stats = await fs.stat(resolvedPath);
      if (!stats.isDirectory()) {
        throw new Error('Path is not a directory');
      }

      const entries = await this.getDirectoryEntries(resolvedPath, options);
      const output = entries.map(entry => {
        const type = entry.isDirectory ? 'd' : 'f';
        const size = entry.isDirectory ? '' : this.formatBytes(entry.size || 0);
        const modified = entry.modified?.toLocaleDateString() || '';
        return `${type} ${entry.name.padEnd(30)} ${size.padEnd(10)} ${modified}`;
      }).join('\n');

      return {
        id: resultId,
        toolCallId: '',
        success: true,
        output: `Directory listing for ${resolvedPath}:\n\nType Name${' '.repeat(26)} Size       Modified\n${'='.repeat(60)}\n${output}`,
        executionTime: Date.now() - startTime,
        metadata: {
          path: resolvedPath,
          entryCount: entries.length,
          recursive: options.recursive,
          showHidden: options.showHidden,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  private async getDirectoryEntries(dirPath: string, options: { recursive?: boolean; showHidden?: boolean }, depth = 0): Promise<Array<{
    name: string;
    isDirectory: boolean;
    size?: number;
    modified?: Date;
  }>> {
    const entries: Array<{
      name: string;
      isDirectory: boolean;
      size?: number;
      modified?: Date;
    }> = [];

    const items = await fs.readdir(dirPath, { withFileTypes: true });

    for (const item of items) {
      // Skip hidden files unless requested
      if (!options.showHidden && item.name.startsWith('.')) {
        continue;
      }

      const fullPath = path.join(dirPath, item.name);
      const stats = await fs.stat(fullPath);
      const indent = '  '.repeat(depth);

      entries.push({
        name: `${indent}${item.name}`,
        isDirectory: item.isDirectory(),
        size: item.isFile() ? stats.size : undefined,
        modified: stats.mtime,
      });

      // Recursively process subdirectories if requested and depth limit not exceeded
      if (options.recursive && item.isDirectory() && depth < 5) {
        const subEntries = await this.getDirectoryEntries(fullPath, options, depth + 1);
        entries.push(...subEntries);
      }
    }

    return entries;
  }

  async watch(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const watchPath = args.path as string;
      const recursive = args.recursive as boolean;

      if (!watchPath) {
        throw new Error('Watch path is required');
      }

      const resolvedPath = path.resolve(watchPath);

      // Security check
      if (!this.isPathSafe(resolvedPath)) {
        throw new Error('Access denied: Path is outside allowed directory');
      }

      // Stop existing watcher if any
      await this.stopWatch(resolvedPath);

      // Create new watcher
      const watcher = chokidar.watch(resolvedPath, {
        ignoreInitial: true,
        persistent: true,
        depth: recursive ? undefined : 0,
      });

      // Set up event handlers
      watcher
        .on('add', (filePath) => {
          this.emit('file-added', { path: filePath, type: 'file' });
          logger.debug('File added', { path: filePath });
        })
        .on('change', (filePath) => {
          this.emit('file-changed', { path: filePath, type: 'file' });
          logger.debug('File changed', { path: filePath });
        })
        .on('unlink', (filePath) => {
          this.emit('file-removed', { path: filePath, type: 'file' });
          logger.debug('File removed', { path: filePath });
        })
        .on('addDir', (dirPath) => {
          this.emit('directory-added', { path: dirPath, type: 'directory' });
          logger.debug('Directory added', { path: dirPath });
        })
        .on('unlinkDir', (dirPath) => {
          this.emit('directory-removed', { path: dirPath, type: 'directory' });
          logger.debug('Directory removed', { path: dirPath });
        })
        .on('error', (error) => {
          this.emit('watch-error', { path: resolvedPath, error });
          logger.error('File watcher error', error);
        });

      this.watchers.set(resolvedPath, watcher);

      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: true,
        output: `Started watching ${resolvedPath} (recursive: ${recursive})`,
        executionTime: Date.now() - startTime,
        metadata: {
          path: resolvedPath,
          recursive,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: (args.toolCallId as string) || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  async stopWatch(watchPath: string): Promise<void> {
    const resolvedPath = path.resolve(watchPath);
    const watcher = this.watchers.get(resolvedPath);

    if (watcher) {
      await watcher.close();
      this.watchers.delete(resolvedPath);
    }
  }

  async stopAllWatchers(): Promise<void> {
    for (const [, watcher] of this.watchers) {
      await watcher.close();
    }
    this.watchers.clear();
  }
}
