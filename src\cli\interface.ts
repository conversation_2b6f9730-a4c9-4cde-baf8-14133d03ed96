import { EventEmitter } from 'events';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora, { Ora } from 'ora';
import boxen from 'boxen';
import { Agent } from '@/core/agent';
import { logger } from '@/utils/logger';
import type {
  StreamChunk,
  ConversationMessage,
  AgentConfig
} from '@/types';

export interface CLIOptions {
  interactive?: boolean;
  verbose?: boolean;
  quiet?: boolean;
  config?: string;
}

export class CLIInterface extends EventEmitter {
  private agent: Agent | null = null;
  private spinner: Ora | null = null;
  private verbose = false;
  private quiet = false;

  constructor(options: CLIOptions = {}) {
    super();
    this.verbose = options.verbose ?? false;
    this.quiet = options.quiet ?? false;
  }

  async initialize(config: AgentConfig): Promise<void> {
    try {
      this.showWelcome();
      
      this.spinner = ora('Initializing Kritrima AI CLI...').start();
      
      this.agent = new Agent(config);
      await this.agent.initialize();
      
      this.setupEventHandlers();
      
      this.spinner.succeed('Kritrima AI CLI initialized successfully');
      this.showStatus();
      
    } catch (error) {
      if (this.spinner) {
        this.spinner.fail('Failed to initialize Kritrima AI CLI');
      }
      this.showError('Initialization failed', error);
      throw error;
    }
  }

  private setupEventHandlers(): void {
    if (!this.agent) return;

    this.agent.on('message-added', (message: ConversationMessage) => {
      if (this.verbose) {
        this.log(`Message added: ${message.role}`, 'debug');
      }
    });

    this.agent.on('tool-executed', (result) => {
      if (this.verbose) {
        this.log(`Tool executed: ${result.success ? 'success' : 'failed'}`, 'debug');
      }
    });

    this.agent.on('context-updated', () => {
      if (this.verbose) {
        this.log('Context updated', 'debug');
      }
    });

    this.agent.on('approval-required', () => {
      // This is handled in the approval system
    });
  }

  async startInteractiveMode(): Promise<void> {
    if (!this.agent) {
      throw new Error('Agent not initialized');
    }

    this.log('Starting interactive mode. Type "exit" to quit, "help" for commands.', 'info');
    
    while (true) {
      try {
        const input = await this.promptUser();
        
        if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
          break;
        }
        
        if (input.toLowerCase() === 'help') {
          this.showHelp();
          continue;
        }
        
        if (input.toLowerCase() === 'status') {
          this.showStatus();
          continue;
        }
        
        if (input.toLowerCase() === 'sessions') {
          await this.showSessions();
          continue;
        }
        
        if (input.toLowerCase().startsWith('switch ')) {
          const sessionId = input.substring(7).trim();
          await this.switchSession(sessionId);
          continue;
        }
        
        if (input.toLowerCase() === 'clear') {
          console.clear();
          this.showWelcome();
          continue;
        }

        if (input.toLowerCase() === 'context') {
          await this.showCurrentContext();
          continue;
        }

        if (input.toLowerCase() === 'tools') {
          await this.showAvailableTools();
          continue;
        }

        if (input.toLowerCase() === 'refresh') {
          await this.refreshContext();
          continue;
        }

        if (input.toLowerCase().startsWith('config ')) {
          const configAction = input.substring(7).trim();
          await this.handleConfigCommand(configAction);
          continue;
        }

        if (input.trim()) {
          await this.processMessage(input);
        }
        
      } catch (error) {
        this.showError('Error in interactive mode', error);
      }
    }
    
    this.log('Goodbye!', 'info');
  }

  async processMessage(message: string): Promise<void> {
    if (!this.agent) {
      throw new Error('Agent not initialized');
    }

    try {
      this.spinner = ora('Processing...').start();
      
      const response = await this.agent.processMessage(message, {
        onChunk: (chunk) => this.handleStreamChunk(chunk),
        onError: (error) => this.handleStreamError(error),
        onComplete: () => this.handleStreamComplete(),
      });
      
      if (this.spinner) {
        this.spinner.stop();
      }
      
      this.displayResponse(response);
      
    } catch (error) {
      if (this.spinner) {
        this.spinner.fail('Processing failed');
      }
      this.showError('Failed to process message', error);
    }
  }

  private async promptUser(): Promise<string> {
    const session = await this.agent?.getSession();
    const prompt = session 
      ? chalk.cyan(`[${session.id.slice(0, 8)}] kritrima> `)
      : chalk.cyan('kritrima> ');
    
    const answer = await inquirer.prompt([
      {
        type: 'input',
        name: 'message',
        message: prompt,
        validate: (input) => input.trim().length > 0 || 'Please enter a message',
      },
    ]);
    
    return answer.message;
  }

  private handleStreamChunk(chunk: StreamChunk): void {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = null;
    }

    switch (chunk.type) {
      case 'text':
        process.stdout.write(chunk.content);
        break;
      case 'tool_call':
        this.log(`\n🔧 ${chunk.content}`, 'info');
        break;
      case 'tool_result':
        this.log(`✅ ${chunk.content}`, 'success');
        break;
      case 'error':
        this.log(`❌ ${chunk.content}`, 'error');
        break;
    }
  }

  private handleStreamError(error: Error): void {
    if (this.spinner) {
      this.spinner.fail('Stream error');
    }
    this.showError('Stream error', error);
  }

  private handleStreamComplete(): void {
    if (this.spinner) {
      this.spinner.stop();
    }
    console.log('\n');
  }

  private displayResponse(response: ConversationMessage): void {
    if (response.content) {
      console.log('\n' + chalk.blue('Assistant:'));
      console.log(response.content);
    }
    
    if (response.toolCalls && response.toolCalls.length > 0) {
      console.log('\n' + chalk.yellow('Tools used:'));
      response.toolCalls.forEach(tool => {
        console.log(`  • ${tool.name}`);
      });
    }
    
    console.log('');
  }

  private showWelcome(): void {
    if (this.quiet) return;
    
    const welcome = boxen(
      chalk.bold.blue('🤖 Kritrima AI CLI') + '\n\n' +
      'Your intelligent command-line assistant\n' +
      'Powered by advanced AI with agentic capabilities\n\n' +
      chalk.dim('Type "help" for available commands'),
      {
        padding: 1,
        margin: 1,
        borderStyle: 'round',
        borderColor: 'blue',
      }
    );
    
    console.log(welcome);
  }

  private showHelp(): void {
    const help = `
${chalk.bold('Available Commands:')}

${chalk.cyan('help')}        - Show this help message
${chalk.cyan('status')}      - Show current status and configuration
${chalk.cyan('context')}     - Show current project context and environment
${chalk.cyan('sessions')}    - List all sessions
${chalk.cyan('switch <id>')} - Switch to a different session
${chalk.cyan('clear')}       - Clear the screen
${chalk.cyan('exit/quit')}   - Exit the CLI

${chalk.bold('AI Commands:')}
You can ask the AI to help with any task, such as:
• File operations (read, write, create, delete)
• Shell commands execution
• Project analysis and development
• System information gathering
• Code generation and debugging

${chalk.bold('Examples:')}
• "List all files in the current directory"
• "Create a new React component called Button"
• "Show me the system information"
• "Install the latest version of Node.js dependencies"
• "Find all TypeScript files containing 'interface'"

${chalk.bold('Safety:')}
The AI will ask for approval before executing potentially dangerous commands.
You can configure the approval mode in the settings.
`;
    
    console.log(help);
  }

  private showStatus(): void {
    if (!this.agent) {
      this.log('Agent not initialized', 'error');
      return;
    }
    
    const status = this.agent.getStatus();
    const config = this.agent.getConfig();
    
    const statusInfo = `
${chalk.bold('Status:')} ${status.ready ? chalk.green('Ready') : chalk.red('Not Ready')}
${chalk.bold('Processing:')} ${status.processing ? chalk.yellow('Yes') : chalk.green('No')}
${chalk.bold('Session:')} ${status.sessionId?.slice(0, 8) || 'None'}
${chalk.bold('Provider:')} ${config.provider}
${chalk.bold('Model:')} ${config.model}
${chalk.bold('Approval Mode:')} ${config.approvalMode}
${chalk.bold('Working Directory:')} ${config.workingDirectory}
`;
    
    console.log(statusInfo);
  }

  private async showSessions(): Promise<void> {
    if (!this.agent) return;
    
    try {
      const sessions = await this.agent.getSessions();
      
      if (sessions.length === 0) {
        this.log('No sessions found', 'info');
        return;
      }
      
      console.log(chalk.bold('\nSessions:'));
      console.log('ID\t\tCreated\t\t\tDirectory');
      console.log('--\t\t-------\t\t\t---------');
      
      sessions.forEach(session => {
        const id = session.id.slice(0, 8);
        const created = session.createdAt.toLocaleDateString();
        const dir = session.workingDirectory;
        console.log(`${id}\t\t${created}\t\t${dir}`);
      });
      
    } catch (error) {
      this.showError('Failed to load sessions', error);
    }
  }

  private async switchSession(sessionId: string): Promise<void> {
    if (!this.agent) return;

    try {
      await this.agent.switchSession(sessionId);
      this.log(`Switched to session ${sessionId}`, 'success');
    } catch (error) {
      this.showError('Failed to switch session', error);
    }
  }

  private async showCurrentContext(): Promise<void> {
    if (!this.agent) return;

    try {
      const context = await this.agent.getContext();
      const { projectStructure, environmentInfo } = context;

      console.log(chalk.bold('\n📁 Current Context:'));
      console.log(`${chalk.bold('Working Directory:')} ${projectStructure.root}`);
      console.log(`${chalk.bold('Project Type:')} ${projectStructure.type || 'Unknown'}`);
      console.log(`${chalk.bold('Language:')} ${projectStructure.language || 'Unknown'}`);
      console.log(`${chalk.bold('Framework:')} ${projectStructure.framework || 'None'}`);
      console.log(`${chalk.bold('Package Manager:')} ${projectStructure.packageManager || 'None'}`);
      console.log(`${chalk.bold('Platform:')} ${environmentInfo.platform}`);
      console.log(`${chalk.bold('Node Version:')} ${environmentInfo.nodeVersion}`);
      console.log(`${chalk.bold('Files Indexed:')} ${projectStructure.files.length}`);
      console.log(`${chalk.bold('Dependencies:')} ${projectStructure.dependencies.length}`);

      if (projectStructure.scripts && Object.keys(projectStructure.scripts).length > 0) {
        console.log(`${chalk.bold('Available Scripts:')} ${Object.keys(projectStructure.scripts).join(', ')}`);
      }

    } catch (error) {
      this.showError('Failed to show context', error);
    }
  }

  private log(message: string, level: 'info' | 'success' | 'error' | 'debug' = 'info'): void {
    if (this.quiet && level !== 'error') return;
    if (!this.verbose && level === 'debug') return;
    
    const colors = {
      info: chalk.blue,
      success: chalk.green,
      error: chalk.red,
      debug: chalk.gray,
    };
    
    const icons = {
      info: 'ℹ',
      success: '✅',
      error: '❌',
      debug: '🔍',
    };
    
    console.log(colors[level](`${icons[level]} ${message}`));
  }

  private showError(message: string, error: unknown): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    console.log(chalk.red(`❌ ${message}: ${errorMessage}`));
    
    if (this.verbose && error instanceof Error && error.stack) {
      console.log(chalk.gray(error.stack));
    }
    
    logger.error(message, error);
  }

  private async showAvailableTools(): Promise<void> {
    if (!this.agent) return;

    try {
      const tools = this.agent['toolRegistry'].getAvailableTools();

      console.log(chalk.bold('\n🔧 Available Tools:'));
      console.log('Name\t\t\tDescription');
      console.log('----\t\t\t-----------');

      tools.forEach(tool => {
        const name = tool.name.padEnd(20);
        const description = tool.description.substring(0, 60);
        console.log(`${name}\t${description}`);
      });

      console.log(`\nTotal: ${tools.length} tools available`);
    } catch (error) {
      this.showError('Failed to show tools', error);
    }
  }

  private async refreshContext(): Promise<void> {
    if (!this.agent) return;

    try {
      this.spinner = ora('Refreshing context...').start();
      await this.agent.refreshContext();
      this.spinner.succeed('Context refreshed successfully');
    } catch (error) {
      if (this.spinner) {
        this.spinner.fail('Failed to refresh context');
      }
      this.showError('Failed to refresh context', error);
    }
  }

  private async handleConfigCommand(action: string): Promise<void> {
    if (!this.agent) return;

    try {
      const parts = action.split(' ');
      const command = parts[0];

      switch (command) {
        case 'show':
          const config = this.agent.getConfig();
          console.log(chalk.bold('\n⚙️ Current Configuration:'));
          console.log(JSON.stringify(config, null, 2));
          break;

        case 'set':
          if (parts.length < 3) {
            this.log('Usage: config set <key> <value>', 'error');
            return;
          }
          const key = parts[1];
          const value = parts.slice(2).join(' ');
          await this.updateConfigValue(key, value);
          break;

        case 'reset':
          const confirm = await inquirer.prompt([{
            type: 'confirm',
            name: 'reset',
            message: 'Are you sure you want to reset configuration to defaults?',
            default: false,
          }]);

          if (confirm.reset) {
            // Reset logic would go here
            this.log('Configuration reset (not implemented)', 'info');
          }
          break;

        default:
          this.log('Available config commands: show, set <key> <value>, reset', 'info');
      }
    } catch (error) {
      this.showError('Failed to handle config command', error);
    }
  }

  private async updateConfigValue(key: string, value: string): Promise<void> {
    try {
      const config = this.agent!.getConfig();
      const newConfig = { ...config };

      // Parse value based on key
      switch (key) {
        case 'temperature':
        case 'maxTokens':
          (newConfig as any)[key] = parseFloat(value);
          break;
        case 'approvalMode':
          if (['suggest', 'auto-edit', 'full-auto'].includes(value)) {
            (newConfig as any)[key] = value;
          } else {
            throw new Error('Invalid approval mode. Use: suggest, auto-edit, or full-auto');
          }
          break;
        default:
          (newConfig as any)[key] = value;
      }

      await this.agent!.updateConfig(newConfig);
      this.log(`Configuration updated: ${key} = ${value}`, 'success');
    } catch (error) {
      this.showError('Failed to update configuration', error);
    }
  }

  async shutdown(): Promise<void> {
    try {
      if (this.spinner) {
        this.spinner.stop();
      }

      if (this.agent) {
        await this.agent.shutdown();
      }

      this.removeAllListeners();

    } catch (error) {
      this.showError('Error during shutdown', error);
    }
  }
}
