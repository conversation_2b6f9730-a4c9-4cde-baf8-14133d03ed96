{"name": "kritrima-ai-cli", "version": "1.0.0", "description": "Production-ready AI-powered CLI tool system with agentic capabilities", "main": "dist/index.js", "bin": {"kritrima": "./bin/kritrima"}, "scripts": {"build": "tsc && tsc-alias", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "node test-system.js", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "prepare": "npm run build"}, "keywords": ["ai", "cli", "agent", "automation", "typescript", "openai", "deepseek", "ollama"], "author": "Kritrima AI CLI", "license": "MIT", "dependencies": {"ansi-escapes": "^6.2.1", "boxen": "^7.1.1", "chalk": "^5.3.0", "chokidar": "^3.6.0", "commander": "^12.0.0", "cross-spawn": "^7.0.3", "dotenv": "^16.4.5", "fast-glob": "^3.3.2", "glob": "^10.4.5", "inquirer": "^9.2.15", "minimatch": "^9.0.3", "nanoid": "^5.0.6", "openai": "^4.95.1", "ora": "^8.0.1", "semver": "^7.6.0", "strip-ansi": "^7.1.0", "terminal-kit": "^3.1.2", "winston": "^3.11.0", "yaml": "^2.4.1", "zod": "^3.22.4"}, "devDependencies": {"@types/better-sqlite3": "^7.6.9", "@types/cross-spawn": "^6.0.6", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@types/semver": "^7.5.8", "@types/terminal-kit": "^2.5.7", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "jest": "^29.7.0", "prettier": "^3.2.5", "ts-jest": "^29.1.2", "tsc-alias": "^1.8.16", "tsx": "^4.7.1", "typescript": "^5.8.3"}, "engines": {"node": ">=20.0.0"}, "repository": {"type": "git", "url": "https://github.com/kritrima/ai-cli.git"}, "bugs": {"url": "https://github.com/kritrima/ai-cli/issues"}, "homepage": "https://github.com/kritrima/ai-cli#readme"}