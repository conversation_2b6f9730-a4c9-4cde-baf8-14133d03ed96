import { EventEmitter } from 'events';
import type { ToolResult } from '../types';
export declare class FileTool extends EventEmitter {
    private readonly maxFileSize;
    private readonly backupDir;
    private watchers;
    constructor();
    read(args: Record<string, unknown>): Promise<ToolResult>;
    write(args: Record<string, unknown>): Promise<ToolResult>;
    create(args: Record<string, unknown>): Promise<ToolResult>;
    private readFilePartial;
    private isPathSafe;
    private formatBytes;
    createBackup(filePath: string): Promise<string>;
    delete(args: Record<string, unknown>): Promise<ToolResult>;
    move(args: Record<string, unknown>): Promise<ToolResult>;
    copy(args: Record<string, unknown>): Promise<ToolResult>;
    private copyDirectory;
    search(args: Record<string, unknown>): Promise<ToolResult>;
    grep(args: Record<string, unknown>): Promise<ToolResult>;
    private searchInFile;
    getFileInfo(filePath: string): Promise<ToolResult>;
    private isTextFile;
    listDirectory(dirPath: string, options?: {
        recursive?: boolean;
        showHidden?: boolean;
    }): Promise<ToolResult>;
    private getDirectoryEntries;
    watch(args: Record<string, unknown>): Promise<ToolResult>;
    stopWatch(watchPath: string): Promise<void>;
    stopAllWatchers(): Promise<void>;
}
//# sourceMappingURL=files.d.ts.map