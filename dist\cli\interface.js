"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLIInterface = void 0;
const events_1 = require("events");
const inquirer_1 = __importDefault(require("inquirer"));
const chalk_1 = __importDefault(require("chalk"));
const ora_1 = __importDefault(require("ora"));
const boxen_1 = __importDefault(require("boxen"));
const agent_1 = require("../core/agent");
const logger_1 = require("../utils/logger");
class CLIInterface extends events_1.EventEmitter {
    agent = null;
    spinner = null;
    verbose = false;
    quiet = false;
    constructor(options = {}) {
        super();
        this.verbose = options.verbose ?? false;
        this.quiet = options.quiet ?? false;
    }
    async initialize(config) {
        try {
            this.showWelcome();
            this.spinner = (0, ora_1.default)('Initializing Kritrima AI CLI...').start();
            this.agent = new agent_1.Agent(config);
            await this.agent.initialize();
            this.setupEventHandlers();
            this.spinner.succeed('Kritrima AI CLI initialized successfully');
            this.showStatus();
        }
        catch (error) {
            if (this.spinner) {
                this.spinner.fail('Failed to initialize Kritrima AI CLI');
            }
            this.showError('Initialization failed', error);
            throw error;
        }
    }
    setupEventHandlers() {
        if (!this.agent)
            return;
        this.agent.on('message-added', (message) => {
            if (this.verbose) {
                this.log(`Message added: ${message.role}`, 'debug');
            }
        });
        this.agent.on('tool-executed', (result) => {
            if (this.verbose) {
                this.log(`Tool executed: ${result.success ? 'success' : 'failed'}`, 'debug');
            }
        });
        this.agent.on('context-updated', () => {
            if (this.verbose) {
                this.log('Context updated', 'debug');
            }
        });
        this.agent.on('approval-required', () => {
            // This is handled in the approval system
        });
    }
    async startInteractiveMode() {
        if (!this.agent) {
            throw new Error('Agent not initialized');
        }
        this.log('Starting interactive mode. Type "exit" to quit, "help" for commands.', 'info');
        while (true) {
            try {
                const input = await this.promptUser();
                if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
                    break;
                }
                if (input.toLowerCase() === 'help') {
                    this.showHelp();
                    continue;
                }
                if (input.toLowerCase() === 'status') {
                    this.showStatus();
                    continue;
                }
                if (input.toLowerCase() === 'sessions') {
                    await this.showSessions();
                    continue;
                }
                if (input.toLowerCase().startsWith('switch ')) {
                    const sessionId = input.substring(7).trim();
                    await this.switchSession(sessionId);
                    continue;
                }
                if (input.toLowerCase() === 'clear') {
                    console.clear();
                    this.showWelcome();
                    continue;
                }
                if (input.toLowerCase() === 'context') {
                    await this.showCurrentContext();
                    continue;
                }
                if (input.toLowerCase() === 'tools') {
                    await this.showAvailableTools();
                    continue;
                }
                if (input.toLowerCase() === 'refresh') {
                    await this.refreshContext();
                    continue;
                }
                if (input.toLowerCase().startsWith('config ')) {
                    const configAction = input.substring(7).trim();
                    await this.handleConfigCommand(configAction);
                    continue;
                }
                if (input.trim()) {
                    await this.processMessage(input);
                }
            }
            catch (error) {
                this.showError('Error in interactive mode', error);
            }
        }
        this.log('Goodbye!', 'info');
    }
    async processMessage(message) {
        if (!this.agent) {
            throw new Error('Agent not initialized');
        }
        try {
            this.spinner = (0, ora_1.default)('Processing...').start();
            const response = await this.agent.processMessage(message, {
                onChunk: (chunk) => this.handleStreamChunk(chunk),
                onError: (error) => this.handleStreamError(error),
                onComplete: () => this.handleStreamComplete(),
            });
            if (this.spinner) {
                this.spinner.stop();
            }
            this.displayResponse(response);
        }
        catch (error) {
            if (this.spinner) {
                this.spinner.fail('Processing failed');
            }
            this.showError('Failed to process message', error);
        }
    }
    async promptUser() {
        const session = await this.agent?.getSession();
        const prompt = session
            ? chalk_1.default.cyan(`[${session.id.slice(0, 8)}] kritrima> `)
            : chalk_1.default.cyan('kritrima> ');
        const answer = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'message',
                message: prompt,
                validate: (input) => input.trim().length > 0 || 'Please enter a message',
            },
        ]);
        return answer.message;
    }
    handleStreamChunk(chunk) {
        if (this.spinner) {
            this.spinner.stop();
            this.spinner = null;
        }
        switch (chunk.type) {
            case 'text':
                process.stdout.write(chunk.content);
                break;
            case 'tool_call':
                this.log(`\n🔧 ${chunk.content}`, 'info');
                break;
            case 'tool_result':
                this.log(`✅ ${chunk.content}`, 'success');
                break;
            case 'error':
                this.log(`❌ ${chunk.content}`, 'error');
                break;
        }
    }
    handleStreamError(error) {
        if (this.spinner) {
            this.spinner.fail('Stream error');
        }
        this.showError('Stream error', error);
    }
    handleStreamComplete() {
        if (this.spinner) {
            this.spinner.stop();
        }
        console.log('\n');
    }
    displayResponse(response) {
        if (response.content) {
            console.log('\n' + chalk_1.default.blue('Assistant:'));
            console.log(response.content);
        }
        if (response.toolCalls && response.toolCalls.length > 0) {
            console.log('\n' + chalk_1.default.yellow('Tools used:'));
            response.toolCalls.forEach(tool => {
                console.log(`  • ${tool.name}`);
            });
        }
        console.log('');
    }
    showWelcome() {
        if (this.quiet)
            return;
        const welcome = (0, boxen_1.default)(chalk_1.default.bold.blue('🤖 Kritrima AI CLI') + '\n\n' +
            'Your intelligent command-line assistant\n' +
            'Powered by advanced AI with agentic capabilities\n\n' +
            chalk_1.default.dim('Type "help" for available commands'), {
            padding: 1,
            margin: 1,
            borderStyle: 'round',
            borderColor: 'blue',
        });
        console.log(welcome);
    }
    showHelp() {
        const help = `
${chalk_1.default.bold('Available Commands:')}

${chalk_1.default.cyan('help')}        - Show this help message
${chalk_1.default.cyan('status')}      - Show current status and configuration
${chalk_1.default.cyan('context')}     - Show current project context and environment
${chalk_1.default.cyan('sessions')}    - List all sessions
${chalk_1.default.cyan('switch <id>')} - Switch to a different session
${chalk_1.default.cyan('clear')}       - Clear the screen
${chalk_1.default.cyan('exit/quit')}   - Exit the CLI

${chalk_1.default.bold('AI Commands:')}
You can ask the AI to help with any task, such as:
• File operations (read, write, create, delete)
• Shell commands execution
• Project analysis and development
• System information gathering
• Code generation and debugging

${chalk_1.default.bold('Examples:')}
• "List all files in the current directory"
• "Create a new React component called Button"
• "Show me the system information"
• "Install the latest version of Node.js dependencies"
• "Find all TypeScript files containing 'interface'"

${chalk_1.default.bold('Safety:')}
The AI will ask for approval before executing potentially dangerous commands.
You can configure the approval mode in the settings.
`;
        console.log(help);
    }
    showStatus() {
        if (!this.agent) {
            this.log('Agent not initialized', 'error');
            return;
        }
        const status = this.agent.getStatus();
        const config = this.agent.getConfig();
        const statusInfo = `
${chalk_1.default.bold('Status:')} ${status.ready ? chalk_1.default.green('Ready') : chalk_1.default.red('Not Ready')}
${chalk_1.default.bold('Processing:')} ${status.processing ? chalk_1.default.yellow('Yes') : chalk_1.default.green('No')}
${chalk_1.default.bold('Session:')} ${status.sessionId?.slice(0, 8) || 'None'}
${chalk_1.default.bold('Provider:')} ${config.provider}
${chalk_1.default.bold('Model:')} ${config.model}
${chalk_1.default.bold('Approval Mode:')} ${config.approvalMode}
${chalk_1.default.bold('Working Directory:')} ${config.workingDirectory}
`;
        console.log(statusInfo);
    }
    async showSessions() {
        if (!this.agent)
            return;
        try {
            const sessions = await this.agent.getSessions();
            if (sessions.length === 0) {
                this.log('No sessions found', 'info');
                return;
            }
            console.log(chalk_1.default.bold('\nSessions:'));
            console.log('ID\t\tCreated\t\t\tDirectory');
            console.log('--\t\t-------\t\t\t---------');
            sessions.forEach(session => {
                const id = session.id.slice(0, 8);
                const created = session.createdAt.toLocaleDateString();
                const dir = session.workingDirectory;
                console.log(`${id}\t\t${created}\t\t${dir}`);
            });
        }
        catch (error) {
            this.showError('Failed to load sessions', error);
        }
    }
    async switchSession(sessionId) {
        if (!this.agent)
            return;
        try {
            await this.agent.switchSession(sessionId);
            this.log(`Switched to session ${sessionId}`, 'success');
        }
        catch (error) {
            this.showError('Failed to switch session', error);
        }
    }
    async showCurrentContext() {
        if (!this.agent)
            return;
        try {
            const context = await this.agent.getContext();
            const { projectStructure, environmentInfo } = context;
            console.log(chalk_1.default.bold('\n📁 Current Context:'));
            console.log(`${chalk_1.default.bold('Working Directory:')} ${projectStructure.root}`);
            console.log(`${chalk_1.default.bold('Project Type:')} ${projectStructure.type || 'Unknown'}`);
            console.log(`${chalk_1.default.bold('Language:')} ${projectStructure.language || 'Unknown'}`);
            console.log(`${chalk_1.default.bold('Framework:')} ${projectStructure.framework || 'None'}`);
            console.log(`${chalk_1.default.bold('Package Manager:')} ${projectStructure.packageManager || 'None'}`);
            console.log(`${chalk_1.default.bold('Platform:')} ${environmentInfo.platform}`);
            console.log(`${chalk_1.default.bold('Node Version:')} ${environmentInfo.nodeVersion}`);
            console.log(`${chalk_1.default.bold('Files Indexed:')} ${projectStructure.files.length}`);
            console.log(`${chalk_1.default.bold('Dependencies:')} ${projectStructure.dependencies.length}`);
            if (projectStructure.scripts && Object.keys(projectStructure.scripts).length > 0) {
                console.log(`${chalk_1.default.bold('Available Scripts:')} ${Object.keys(projectStructure.scripts).join(', ')}`);
            }
        }
        catch (error) {
            this.showError('Failed to show context', error);
        }
    }
    log(message, level = 'info') {
        if (this.quiet && level !== 'error')
            return;
        if (!this.verbose && level === 'debug')
            return;
        const colors = {
            info: chalk_1.default.blue,
            success: chalk_1.default.green,
            error: chalk_1.default.red,
            debug: chalk_1.default.gray,
        };
        const icons = {
            info: 'ℹ',
            success: '✅',
            error: '❌',
            debug: '🔍',
        };
        console.log(colors[level](`${icons[level]} ${message}`));
    }
    showError(message, error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.log(chalk_1.default.red(`❌ ${message}: ${errorMessage}`));
        if (this.verbose && error instanceof Error && error.stack) {
            console.log(chalk_1.default.gray(error.stack));
        }
        logger_1.logger.error(message, error);
    }
    async showAvailableTools() {
        if (!this.agent)
            return;
        try {
            const tools = this.agent['toolRegistry'].getAvailableTools();
            console.log(chalk_1.default.bold('\n🔧 Available Tools:'));
            console.log('Name\t\t\tDescription');
            console.log('----\t\t\t-----------');
            tools.forEach(tool => {
                const name = tool.name.padEnd(20);
                const description = tool.description.substring(0, 60);
                console.log(`${name}\t${description}`);
            });
            console.log(`\nTotal: ${tools.length} tools available`);
        }
        catch (error) {
            this.showError('Failed to show tools', error);
        }
    }
    async refreshContext() {
        if (!this.agent)
            return;
        try {
            this.spinner = (0, ora_1.default)('Refreshing context...').start();
            await this.agent.refreshContext();
            this.spinner.succeed('Context refreshed successfully');
        }
        catch (error) {
            if (this.spinner) {
                this.spinner.fail('Failed to refresh context');
            }
            this.showError('Failed to refresh context', error);
        }
    }
    async handleConfigCommand(action) {
        if (!this.agent)
            return;
        try {
            const parts = action.split(' ');
            const command = parts[0];
            switch (command) {
                case 'show':
                    const config = this.agent.getConfig();
                    console.log(chalk_1.default.bold('\n⚙️ Current Configuration:'));
                    console.log(JSON.stringify(config, null, 2));
                    break;
                case 'set':
                    if (parts.length < 3) {
                        this.log('Usage: config set <key> <value>', 'error');
                        return;
                    }
                    const key = parts[1];
                    const value = parts.slice(2).join(' ');
                    await this.updateConfigValue(key, value);
                    break;
                case 'reset':
                    const confirm = await inquirer_1.default.prompt([{
                            type: 'confirm',
                            name: 'reset',
                            message: 'Are you sure you want to reset configuration to defaults?',
                            default: false,
                        }]);
                    if (confirm.reset) {
                        // Reset logic would go here
                        this.log('Configuration reset (not implemented)', 'info');
                    }
                    break;
                default:
                    this.log('Available config commands: show, set <key> <value>, reset', 'info');
            }
        }
        catch (error) {
            this.showError('Failed to handle config command', error);
        }
    }
    async updateConfigValue(key, value) {
        try {
            const config = this.agent.getConfig();
            const newConfig = { ...config };
            // Parse value based on key
            switch (key) {
                case 'temperature':
                case 'maxTokens':
                    newConfig[key] = parseFloat(value);
                    break;
                case 'approvalMode':
                    if (['suggest', 'auto-edit', 'full-auto'].includes(value)) {
                        newConfig[key] = value;
                    }
                    else {
                        throw new Error('Invalid approval mode. Use: suggest, auto-edit, or full-auto');
                    }
                    break;
                default:
                    newConfig[key] = value;
            }
            await this.agent.updateConfig(newConfig);
            this.log(`Configuration updated: ${key} = ${value}`, 'success');
        }
        catch (error) {
            this.showError('Failed to update configuration', error);
        }
    }
    async shutdown() {
        try {
            if (this.spinner) {
                this.spinner.stop();
            }
            if (this.agent) {
                await this.agent.shutdown();
            }
            this.removeAllListeners();
        }
        catch (error) {
            this.showError('Error during shutdown', error);
        }
    }
}
exports.CLIInterface = CLIInterface;
//# sourceMappingURL=interface.js.map