import { logger } from './logger';

export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  exponentialBackoff: boolean;
  jitter: boolean;
  retryCondition?: (error: Error) => boolean;
  onRetry?: (error: Error, attempt: number) => void;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
  totalTime: number;
}

export class RetryManager {
  private static defaultOptions: RetryOptions = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    exponentialBackoff: true,
    jitter: true,
  };

  static async retry<T>(
    operation: () => Promise<T>,
    options: Partial<RetryOptions> = {}
  ): Promise<RetryResult<T>> {
    const config = { ...this.defaultOptions, ...options };
    const startTime = Date.now();
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        const result = await operation();
        return {
          success: true,
          result,
          attempts: attempt,
          totalTime: Date.now() - startTime,
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Check if we should retry this error
        if (config.retryCondition && !config.retryCondition(lastError)) {
          break;
        }

        // Don't wait after the last attempt
        if (attempt === config.maxAttempts) {
          break;
        }

        // Calculate delay
        let delay = config.baseDelay;
        if (config.exponentialBackoff) {
          delay = Math.min(config.baseDelay * Math.pow(2, attempt - 1), config.maxDelay);
        }

        // Add jitter to prevent thundering herd
        if (config.jitter) {
          delay = delay * (0.5 + Math.random() * 0.5);
        }

        // Call retry callback
        if (config.onRetry) {
          config.onRetry(lastError, attempt);
        }

        logger.debug('Retrying operation', {
          attempt,
          maxAttempts: config.maxAttempts,
          delay,
          error: lastError.message,
        });

        await this.sleep(delay);
      }
    }

    return {
      success: false,
      error: lastError || new Error('Operation failed'),
      attempts: config.maxAttempts,
      totalTime: Date.now() - startTime,
    };
  }

  static async retryWithCircuitBreaker<T>(
    operation: () => Promise<T>,
    options: Partial<RetryOptions> & {
      circuitBreakerThreshold?: number;
      circuitBreakerTimeout?: number;
    } = {}
  ): Promise<RetryResult<T>> {
    const circuitBreaker = new CircuitBreaker(
      options.circuitBreakerThreshold || 5,
      options.circuitBreakerTimeout || 60000
    );

    if (circuitBreaker.isOpen()) {
      return {
        success: false,
        error: new Error('Circuit breaker is open'),
        attempts: 0,
        totalTime: 0,
      };
    }

    const result = await this.retry(operation, options);
    
    if (result.success) {
      circuitBreaker.recordSuccess();
    } else {
      circuitBreaker.recordFailure();
    }

    return result;
  }

  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static isRetryableError(error: Error): boolean {
    const retryableErrors = [
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'ENOTFOUND',
      'EAI_AGAIN',
      'EPIPE',
      'ECONNABORTED',
    ];

    const retryableMessages = [
      'timeout',
      'network',
      'connection',
      'rate limit',
      'too many requests',
      'service unavailable',
      'internal server error',
      'bad gateway',
      'gateway timeout',
    ];

    const errorCode = (error as any).code;
    const errorMessage = error.message.toLowerCase();

    return retryableErrors.includes(errorCode) ||
           retryableMessages.some(msg => errorMessage.includes(msg));
  }

  static isRateLimitError(error: Error): boolean {
    const rateLimitMessages = [
      'rate limit',
      'too many requests',
      'quota exceeded',
      'throttled',
    ];

    return rateLimitMessages.some(msg => 
      error.message.toLowerCase().includes(msg)
    );
  }
}

class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';

  constructor(
    private threshold: number,
    private timeout: number
  ) {}

  isOpen(): boolean {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'half-open';
        return false;
      }
      return true;
    }
    return false;
  }

  recordSuccess(): void {
    this.failures = 0;
    this.state = 'closed';
  }

  recordFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'open';
    }
  }
}

export { CircuitBreaker };
