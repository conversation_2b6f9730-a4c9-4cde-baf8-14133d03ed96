"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CircuitBreaker = exports.RetryManager = void 0;
const logger_1 = require("./logger");
class RetryManager {
    static defaultOptions = {
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 30000,
        exponentialBackoff: true,
        jitter: true,
    };
    static async retry(operation, options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const startTime = Date.now();
        let lastError = null;
        for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
            try {
                const result = await operation();
                return {
                    success: true,
                    result,
                    attempts: attempt,
                    totalTime: Date.now() - startTime,
                };
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                // Check if we should retry this error
                if (config.retryCondition && !config.retryCondition(lastError)) {
                    break;
                }
                // Don't wait after the last attempt
                if (attempt === config.maxAttempts) {
                    break;
                }
                // Calculate delay
                let delay = config.baseDelay;
                if (config.exponentialBackoff) {
                    delay = Math.min(config.baseDelay * Math.pow(2, attempt - 1), config.maxDelay);
                }
                // Add jitter to prevent thundering herd
                if (config.jitter) {
                    delay = delay * (0.5 + Math.random() * 0.5);
                }
                // Call retry callback
                if (config.onRetry) {
                    config.onRetry(lastError, attempt);
                }
                logger_1.logger.debug('Retrying operation', {
                    attempt,
                    maxAttempts: config.maxAttempts,
                    delay,
                    error: lastError.message,
                });
                await this.sleep(delay);
            }
        }
        return {
            success: false,
            error: lastError || new Error('Operation failed'),
            attempts: config.maxAttempts,
            totalTime: Date.now() - startTime,
        };
    }
    static async retryWithCircuitBreaker(operation, options = {}) {
        const circuitBreaker = new CircuitBreaker(options.circuitBreakerThreshold || 5, options.circuitBreakerTimeout || 60000);
        if (circuitBreaker.isOpen()) {
            return {
                success: false,
                error: new Error('Circuit breaker is open'),
                attempts: 0,
                totalTime: 0,
            };
        }
        const result = await this.retry(operation, options);
        if (result.success) {
            circuitBreaker.recordSuccess();
        }
        else {
            circuitBreaker.recordFailure();
        }
        return result;
    }
    static sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    static isRetryableError(error) {
        const retryableErrors = [
            'ECONNRESET',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'ENOTFOUND',
            'EAI_AGAIN',
            'EPIPE',
            'ECONNABORTED',
        ];
        const retryableMessages = [
            'timeout',
            'network',
            'connection',
            'rate limit',
            'too many requests',
            'service unavailable',
            'internal server error',
            'bad gateway',
            'gateway timeout',
        ];
        const errorCode = error.code;
        const errorMessage = error.message.toLowerCase();
        return retryableErrors.includes(errorCode) ||
            retryableMessages.some(msg => errorMessage.includes(msg));
    }
    static isRateLimitError(error) {
        const rateLimitMessages = [
            'rate limit',
            'too many requests',
            'quota exceeded',
            'throttled',
        ];
        return rateLimitMessages.some(msg => error.message.toLowerCase().includes(msg));
    }
}
exports.RetryManager = RetryManager;
class CircuitBreaker {
    threshold;
    timeout;
    failures = 0;
    lastFailureTime = 0;
    state = 'closed';
    constructor(threshold, timeout) {
        this.threshold = threshold;
        this.timeout = timeout;
    }
    isOpen() {
        if (this.state === 'open') {
            if (Date.now() - this.lastFailureTime > this.timeout) {
                this.state = 'half-open';
                return false;
            }
            return true;
        }
        return false;
    }
    recordSuccess() {
        this.failures = 0;
        this.state = 'closed';
    }
    recordFailure() {
        this.failures++;
        this.lastFailureTime = Date.now();
        if (this.failures >= this.threshold) {
            this.state = 'open';
        }
    }
}
exports.CircuitBreaker = CircuitBreaker;
//# sourceMappingURL=retry.js.map